#!/usr/bin/env python3
"""
测试 API 密钥检测和系统状态
"""

import requests
import json

def test_api_key_detection():
    """测试 API 密钥检测"""
    print("🧪 测试 NextAI API 密钥检测")
    print("=" * 50)
    
    try:
        # 测试系统状态
        print("1. 📊 检查系统状态...")
        response = requests.get("http://localhost:8000/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统状态: {data['status']}")
            print(f"🤖 引擎类型: {data['engine']['type']}")
            print(f"🔧 当前模型: {data['engine']['current_model']}")
            print(f"📦 LangGraph可用: {data['engine']['langgraph_available']}")
            
            # 检查是否检测到 API 密钥
            if data['engine']['current_model'] != 'None':
                print("✅ API 密钥检测成功!")
                print(f"🎯 使用模型: {data['engine']['current_model']}")
            else:
                print("⚠️ 未检测到可用模型")
        else:
            print(f"❌ 系统状态检查失败: {response.status_code}")
            return
        
        # 测试健康检查
        print("\n2. 🏥 健康检查...")
        response = requests.get("http://localhost:8000/health")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康状态: {data['status']}")
            print(f"🔧 执行引擎: {data['components']['engine_type']}")
        
        # 测试简单任务
        print("\n3. 📝 测试任务执行...")
        test_data = {
            "user_input": "帮我创建一个简单的测试文档",
            "session_id": "test_api_key"
        }
        
        response = requests.post("http://localhost:8000/api/execute", json=test_data)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['success']:
                print("✅ 任务执行成功!")
                print(f"⏱️ 执行时间: {result['execution_time']:.2f}秒")
                print(f"🔧 使用引擎: {result.get('engine', 'unknown')}")
                
                # 检查输出
                if 'result' in result and 'result' in result['result']:
                    output = result['result']['result'].get('output', '')
                    if output:
                        print("\n📤 执行输出预览:")
                        print(output[:300] + "..." if len(output) > 300 else output)
            else:
                print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")
        
        print("\n" + "=" * 50)
        print("🎉 测试完成!")
        
        # 给出建议
        print("\n💡 下一步建议:")
        print("1. 如果看到 '✅ API 密钥检测成功!'，说明配置正确")
        print("2. 运行以下命令安装完整功能:")
        print("   pip install langchain langchain-anthropic")
        print("3. 重启服务后即可使用真正的 AI 功能")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api_key_detection()
