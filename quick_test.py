#!/usr/bin/env python3
"""
快速测试 NextAI 是否修复了错误
"""

import requests
import json

def test_nextai_fix():
    """测试NextAI修复"""
    print("🧪 测试 NextAI 错误修复")
    print("=" * 40)
    
    # 测试API
    try:
        # 1. 测试系统状态
        print("1. 📊 测试系统状态...")
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统状态: {data['status']}")
            print(f"🤖 引擎类型: {data['engine']['type']}")
        else:
            print(f"❌ 系统状态检查失败: {response.status_code}")
            return
        
        # 2. 测试简单任务
        print("\n2. 📝 测试文档创建任务...")
        test_data = {
            "user_input": "帮我创建一个简单的README.md文档",
            "session_id": "test_fix_001"
        }
        
        response = requests.post("http://localhost:8000/api/execute", json=test_data)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ 任务执行成功!")
                print(f"⏱️ 执行时间: {result['execution_time']:.2f}秒")
                
                # 检查是否还有错误
                if 'result' in result and 'result' in result['result']:
                    output = result['result']['result'].get('output', '')
                    if 'name \'key\' is not defined' in output:
                        print("❌ 仍然存在 'key' 未定义错误")
                    else:
                        print("✅ 'key' 错误已修复!")
                        print("📤 执行输出预览:")
                        print(output[:200] + "..." if len(output) > 200 else output)
            else:
                print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
        
        print("\n" + "=" * 40)
        print("🎉 测试完成!")
        print("\n💡 如果看到 '✅ key错误已修复!'，说明问题已解决")
        print("现在可以在前端界面正常使用了！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_nextai_fix()
