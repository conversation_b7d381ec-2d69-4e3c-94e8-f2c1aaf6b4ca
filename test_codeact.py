#!/usr/bin/env python3
"""
测试 NextAI CodeAct 引擎
"""

import asyncio
import json
import aiohttp

async def test_nextai():
    """测试NextAI API"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试 NextAI CodeAct 引擎")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试系统状态
        print("\n1. 📊 检查系统状态...")
        try:
            async with session.get(f"{base_url}/") as resp:
                data = await resp.json()
                print(f"✅ 系统状态: {data['status']}")
                print(f"🤖 引擎类型: {data['engine']['type']}")
                print(f"🔧 当前模型: {data['engine']['current_model']}")
                print(f"📦 LangGraph可用: {data['engine']['langgraph_available']}")
        except Exception as e:
            print(f"❌ 系统状态检查失败: {e}")
            return
        
        # 2. 测试健康检查
        print("\n2. 🏥 健康检查...")
        try:
            async with session.get(f"{base_url}/health") as resp:
                data = await resp.json()
                print(f"✅ 健康状态: {data['status']}")
                print(f"🔧 执行引擎: {data['components']['engine_type']}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 3. 测试简单任务
        print("\n3. 📝 测试文档创建任务...")
        test_request = {
            "user_input": "帮我创建一个简单的README.md文档",
            "session_id": "test_session_001"
        }
        
        try:
            async with session.post(f"{base_url}/api/execute", 
                                   json=test_request) as resp:
                data = await resp.json()
                
                if data['success']:
                    print("✅ 任务执行成功!")
                    result = data['result']
                    print(f"🔧 执行引擎: {data.get('engine', 'unknown')}")
                    print(f"⏱️ 执行时间: {data['execution_time']:.2f}秒")
                    
                    if 'result' in result:
                        print(f"📋 任务类型: {result['result']['type']}")
                        print(f"🐍 执行环境: {result['result']['execution_env']}")
                        
                        if 'code' in result['result']:
                            print("\n📝 生成的代码:")
                            print("-" * 40)
                            print(result['result']['code'][:200] + "..." if len(result['result']['code']) > 200 else result['result']['code'])
                            print("-" * 40)
                        
                        if 'output' in result['result']:
                            print("\n📤 执行输出:")
                            print(result['result']['output'])
                else:
                    print(f"❌ 任务执行失败: {data.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 任务执行测试失败: {e}")
        
        # 4. 测试数学计算
        print("\n4. 🧮 测试数学计算任务...")
        math_request = {
            "user_input": "计算 2 的 10 次方，并解释结果",
            "session_id": "test_session_002"
        }
        
        try:
            async with session.post(f"{base_url}/api/execute", 
                                   json=math_request) as resp:
                data = await resp.json()
                
                if data['success']:
                    print("✅ 数学计算成功!")
                    print(f"⏱️ 执行时间: {data['execution_time']:.2f}秒")
                    
                    result = data['result']
                    if 'result' in result and 'output' in result['result']:
                        print("📤 计算结果:")
                        print(result['result']['output'])
                else:
                    print(f"❌ 数学计算失败: {data.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 数学计算测试失败: {e}")
        
        # 5. 测试统计信息
        print("\n5. 📊 获取统计信息...")
        try:
            async with session.get(f"{base_url}/api/stats") as resp:
                data = await resp.json()
                
                if 'execution' in data:
                    stats = data['execution']
                    print(f"📈 总任务数: {stats.get('total_tasks', 0)}")
                    print(f"✅ 成功任务数: {stats.get('successful_tasks', 0)}")
                    print(f"📊 成功率: {stats.get('success_rate', 0)*100:.1f}%")
                    print(f"🔧 引擎类型: {stats.get('engine_type', 'Unknown')}")
                    
                    if 'features' in stats:
                        print("\n🎯 支持的功能:")
                        for feature in stats['features']:
                            print(f"  {feature}")
                            
        except Exception as e:
            print(f"❌ 统计信息获取失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    print("\n💡 提示:")
    print("- 如果看到 'Local' 引擎，说明在使用本地代码执行")
    print("- 配置API密钥后可获得完整的AI代码生成能力")
    print("- 运行 python setup_nextai.py 进行完整设置")

if __name__ == "__main__":
    asyncio.run(test_nextai())
