# NextAI 环境配置文件
# 复制此文件为 .env 并填入您的 API 密钥

# =============================================================================
# 大模型 API 配置
# =============================================================================

# Anthropic Claude API
# 获取地址: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI GPT API  
# 获取地址: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API
# 获取地址: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# =============================================================================
# 本地模型配置 (Ollama)
# =============================================================================

# Ollama 服务地址 (如果使用本地模型)
OLLAMA_BASE_URL=http://localhost:11434

# =============================================================================
# NextAI 应用配置
# =============================================================================

# 默认使用的模型 (优先级顺序)
DEFAULT_MODEL=claude-3-5-sonnet-20241022
# 备选: gpt-4o-mini, gemini-1.5-flash, ollama/llama3.2

# 服务端口
PORT=8000

# 开发模式
DEBUG=true

# =============================================================================
# 安全配置
# =============================================================================

# 允许的文件操作目录 (安全限制)
ALLOWED_WORK_DIR=./workspace

# 代码执行超时时间 (秒)
CODE_EXECUTION_TIMEOUT=30

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件为 .env
# 2. 填入至少一个模型的 API 密钥
# 3. 如果没有 API 密钥，可以安装 Ollama 使用本地模型:
#    - 安装: https://ollama.ai/
#    - 运行: ollama pull llama3.2
# 4. 重启 NextAI 服务
