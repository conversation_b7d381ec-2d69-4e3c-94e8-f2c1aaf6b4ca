#!/usr/bin/env python3
"""
安装 NextAI 必要的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"📦 安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主安装流程"""
    print("🚀 NextAI 依赖安装程序")
    print("=" * 50)
    
    # 必要的依赖包
    packages = [
        "python-dotenv",
        "langchain",
        "langchain-anthropic", 
        "langchain-openai",
        "langchain-core"
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {success_count}/{len(packages)} 成功")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功!")
        print("\n💡 现在可以重启 NextAI 服务:")
        print("   cd nextai_backend")
        print("   python main.py")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装:")
        failed_packages = packages[success_count:]
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
    
    # 检查环境变量
    print("\n🔑 检查 API 密钥配置...")
    env_file = "nextai_backend/.env"
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            content = f.read()
            if "ANTHROPIC_API_KEY=sk-ant-" in content:
                print("✅ Anthropic API 密钥已配置")
            else:
                print("⚠️ Anthropic API 密钥未配置")
    else:
        print("⚠️ .env 文件不存在")

if __name__ == "__main__":
    main()
