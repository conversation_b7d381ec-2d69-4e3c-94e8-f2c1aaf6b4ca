#!/usr/bin/env python3
"""
测试改进后的MD文件创建功能
"""

import requests
import json
import os

def test_improved_md_creation():
    """测试改进后的MD文件创建"""
    print("🧪 测试改进后的MD文件创建功能")
    print("=" * 50)
    
    # 删除旧文件
    old_files = ["nextai_backend/document.md", "nextai_backend/nextai_document.md"]
    for file_path in old_files:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"🗑️ 删除旧文件: {file_path}")
    
    # 测试新的MD创建
    test_data = {
        "user_input": "帮我创建一个项目介绍的md文档",
        "session_id": "test_improved_md"
    }
    
    try:
        print("\n📝 发送创建MD文档请求...")
        response = requests.post("http://localhost:8000/api/execute", json=test_data)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['success']:
                print("✅ 任务执行成功!")
                print(f"⏱️ 执行时间: {result['execution_time']:.2f}秒")
                
                # 检查生成的代码
                if 'result' in result and 'result' in result['result']:
                    code = result['result']['result'].get('code', '')
                    output = result['result']['result'].get('output', '')
                    
                    print("\n📝 生成的代码特点:")
                    if 'nextai_document.md' in code:
                        print("✅ 使用了新的文件名")
                    if 'NextAI 创建的文档' in code:
                        print("✅ 包含了完整的文档结构")
                    if 'os.path.abspath' in code:
                        print("✅ 显示文件绝对路径")
                    if '功能特点' in code:
                        print("✅ 包含了详细的文档内容")
                    
                    print("\n📤 执行输出:")
                    print(output)
                    
                    # 检查文件是否真的创建了
                    md_file = "nextai_backend/nextai_document.md"
                    if os.path.exists(md_file):
                        print(f"\n✅ 文件创建成功: {md_file}")
                        
                        # 显示文件内容
                        with open(md_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        print(f"📄 文件大小: {len(content)} 字符")
                        print("\n📖 文件内容预览:")
                        print("-" * 40)
                        print(content[:500] + "..." if len(content) > 500 else content)
                        print("-" * 40)
                        
                        # 检查内容质量
                        if "NextAI 创建的文档" in content:
                            print("✅ 包含标准标题")
                        if "用户请求" in content:
                            print("✅ 包含用户请求信息")
                        if "功能特点" in content:
                            print("✅ 包含功能特点说明")
                        if "创建时间" in content:
                            print("✅ 包含创建时间")
                        
                    else:
                        print("❌ 文件未创建")
                        
            else:
                print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")

def test_math_calculation():
    """测试数学计算功能"""
    print("\n🧮 测试数学计算功能")
    print("=" * 30)
    
    test_data = {
        "user_input": "帮我计算圆的面积",
        "session_id": "test_math"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/execute", json=test_data)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['success']:
                output = result['result']['result'].get('output', '')
                print("✅ 数学计算成功!")
                print("📤 计算结果:")
                print(output)
            else:
                print(f"❌ 计算失败: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 数学测试失败: {e}")

if __name__ == "__main__":
    test_improved_md_creation()
    test_math_calculation()
