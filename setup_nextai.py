#!/usr/bin/env python3
"""
NextAI 快速设置脚本
自动安装依赖并配置环境
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 NextAI - 智能代码助手 设置向导")
    print("=" * 60)
    print("集成 LangGraph CodeAct 的真实AI代码执行系统")
    print()

def check_python_version():
    """检查Python版本"""
    print("📋 检查Python版本...")
    if sys.version_info < (3, 10):
        print("❌ 需要Python 3.10或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖包...")
    
    requirements_file = Path("nextai_backend/requirements.txt")
    if not requirements_file.exists():
        print("❌ 找不到 requirements.txt 文件")
        return False
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                               check=True, capture_output=True, text=True)
        print("✅ 依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("输出:", e.stdout)
        print("错误:", e.stderr)
        return False

def setup_env_file():
    """设置环境文件"""
    print("\n🔧 配置环境文件...")
    
    env_example = Path("nextai_backend/.env.example")
    env_file = Path("nextai_backend/.env")
    
    if not env_example.exists():
        print("❌ 找不到 .env.example 文件")
        return False
    
    if env_file.exists():
        print("⚠️ .env 文件已存在")
        choice = input("是否覆盖? (y/N): ").lower()
        if choice != 'y':
            print("📝 保持现有 .env 文件")
            return True
    
    # 复制示例文件
    shutil.copy(env_example, env_file)
    print("✅ 已创建 .env 文件")
    
    # 询问是否配置API密钥
    print("\n🔑 API密钥配置 (可选):")
    print("1. Anthropic Claude API (推荐)")
    print("2. OpenAI GPT API")
    print("3. Google Gemini API")
    print("4. 跳过，稍后手动配置")
    
    choice = input("选择 (1-4): ").strip()
    
    if choice == "1":
        api_key = input("请输入 Anthropic API Key: ").strip()
        if api_key:
            update_env_file(env_file, "ANTHROPIC_API_KEY", api_key)
    elif choice == "2":
        api_key = input("请输入 OpenAI API Key: ").strip()
        if api_key:
            update_env_file(env_file, "OPENAI_API_KEY", api_key)
    elif choice == "3":
        api_key = input("请输入 Google API Key: ").strip()
        if api_key:
            update_env_file(env_file, "GOOGLE_API_KEY", api_key)
    
    return True

def update_env_file(env_file: Path, key: str, value: str):
    """更新环境文件中的值"""
    try:
        content = env_file.read_text(encoding='utf-8')
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if line.startswith(f"{key}="):
                lines[i] = f"{key}={value}"
                break
        
        env_file.write_text('\n'.join(lines), encoding='utf-8')
        print(f"✅ 已设置 {key}")
        
    except Exception as e:
        print(f"❌ 更新环境文件失败: {e}")

def check_ollama():
    """检查Ollama是否可用"""
    print("\n🤖 检查本地模型支持...")
    
    try:
        result = subprocess.run(["ollama", "--version"], 
                               capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Ollama 已安装")
            
            # 检查是否有可用模型
            result = subprocess.run(["ollama", "list"], 
                                   capture_output=True, text=True, timeout=10)
            if "llama" in result.stdout.lower():
                print("✅ 发现本地模型")
                return True
            else:
                print("💡 建议安装本地模型: ollama pull llama3.2")
                
        return True
        
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️ Ollama 未安装")
        print("💡 可选: 安装 Ollama 使用本地模型 (https://ollama.ai/)")
        return False

def create_workspace():
    """创建工作目录"""
    print("\n📁 创建工作目录...")
    
    workspace = Path("nextai_backend/workspace")
    workspace.mkdir(exist_ok=True)
    
    # 创建示例文件
    example_file = workspace / "README.md"
    if not example_file.exists():
        example_file.write_text("""# NextAI 工作空间

这是 NextAI 的工作目录，AI生成的文件将保存在这里。

## 安全说明
- 代码执行被限制在此目录内
- 请勿在此目录外执行不信任的代码

## 开始使用
1. 访问 http://localhost:3000 打开前端界面
2. 输入您的需求，如 "帮我创建一个Python脚本"
3. AI将生成代码并在此目录中执行
""", encoding='utf-8')
    
    print(f"✅ 工作目录: {workspace.absolute()}")

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 NextAI 设置完成!")
    print("=" * 60)
    print()
    print("📋 后续步骤:")
    print("1. 启动后端服务:")
    print("   cd nextai_backend")
    print("   python main.py")
    print()
    print("2. 启动前端服务 (新终端):")
    print("   cd nextai_frontend")
    print("   python -m http.server 3000")
    print()
    print("3. 访问应用:")
    print("   🌐 前端界面: http://localhost:3000")
    print("   📊 API文档: http://localhost:8000/docs")
    print("   ℹ️ 系统状态: http://localhost:8000")
    print()
    print("💡 提示:")
    print("- 如果没有配置API密钥，系统将使用本地代码执行")
    print("- 配置API密钥后可获得完整的AI代码生成能力")
    print("- 编辑 nextai_backend/.env 文件来修改配置")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 设置失败: 依赖安装失败")
        print("💡 请手动运行: pip install -r nextai_backend/requirements.txt")
        sys.exit(1)
    
    # 设置环境文件
    if not setup_env_file():
        print("\n❌ 设置失败: 环境配置失败")
        sys.exit(1)
    
    # 检查Ollama
    check_ollama()
    
    # 创建工作目录
    create_workspace()
    
    # 打印后续步骤
    print_next_steps()

if __name__ == "__main__":
    main()
