<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextAI - 智能代码助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            background: #f7f7f7;
            height: 100vh;
            overflow: hidden;
            color: #333;
        }

        /* 主布局 */
        .app-container {
            display: flex;
            height: 100vh;
            background: #fff;
        }

        /* 左侧对话区域 */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e5e5e5;
            background: #fff;
        }

        /* 右侧代码/终端区域 */
        .code-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1e1e1e;
            color: #d4d4d4;
        }

        /* 顶部标题栏 */
        .header {
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.connected {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }

        .status-badge.disconnected {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        /* 对话消息区域 */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            display: flex;
            gap: 12px;
            max-width: 80%;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message.assistant {
            align-self: flex-start;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #3b82f6;
            color: white;
        }

        .message.assistant .message-avatar {
            background: #10b981;
            color: white;
        }

        .message-content {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 输入区域 */
        .chat-input {
            border-top: 1px solid #e5e5e5;
            padding: 16px 20px;
            background: #fff;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            font-family: inherit;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .input-field:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .send-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .send-button:hover {
            background: #2563eb;
        }

        .send-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        /* 右侧代码面板 */
        .code-header {
            background: #2d2d2d;
            border-bottom: 1px solid #3e3e3e;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
        }

        .code-header h2 {
            font-size: 14px;
            font-weight: 500;
            color: #d4d4d4;
            margin: 0;
        }

        .code-tabs {
            display: flex;
            gap: 8px;
        }

        .code-tab {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #9ca3af;
        }

        .code-tab.active {
            background: #374151;
            color: #d4d4d4;
        }

        .code-tab:hover {
            background: #374151;
        }

        .code-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Consolas', 'SF Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
        }

        .code-block {
            background: #1e1e1e;
            border: 1px solid #3e3e3e;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            color: #d4d4d4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 加载状态 */
        .loading {
            display: none;
            padding: 20px;
            text-align: center;
            color: #6b7280;
        }

        .loading.show {
            display: block;
        }

        .loading .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 执行状态指示器 */
        .execution-status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .execution-status.running {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .execution-status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .execution-status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        /* 快捷操作按钮 */
        .quick-actions {
            padding: 12px 20px;
            border-bottom: 1px solid #e5e5e5;
            background: #f9fafb;
        }

        .quick-actions h3 {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .action-button {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 16px;
            background: white;
            color: #374151;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .code-panel {
                height: 50vh;
            }

            .chat-panel {
                height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧对话面板 -->
        <div class="chat-panel">
            <!-- 顶部标题栏 -->
            <div class="header">
                <h1>NextAI 智能助手</h1>
                <div class="status-badge" id="connectionStatus">
                    <span id="statusText">连接中...</span>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions">
                <h3>快捷操作</h3>
                <div class="action-buttons">
                    <button class="action-button" onclick="loadTestCase('data_analysis')">数据分析</button>
                    <button class="action-button" onclick="loadTestCase('api_call')">API调用</button>
                    <button class="action-button" onclick="loadTestCase('web_automation')">网页自动化</button>
                    <button class="action-button" onclick="loadTestCase('calculation')">数学计算</button>
                    <button class="action-button" onclick="clearChat()">清空对话</button>
                </div>
            </div>

            <!-- 对话消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <div class="message-avatar">AI</div>
                    <div class="message-content">
                        你好！我是NextAI智能助手，可以帮你执行代码、分析文件、处理数据等任务。请告诉我你需要什么帮助？
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input">
                <div class="input-container">
                    <textarea
                        id="userInput"
                        class="input-field"
                        placeholder="输入你的请求..."
                        rows="1"
                    ></textarea>
                    <button class="send-button" onclick="sendMessage()" id="sendButton">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 右侧代码/终端面板 -->
        <div class="code-panel">
            <!-- 代码面板标题栏 -->
            <div class="code-header">
                <h2>执行监控</h2>
                <div class="code-tabs">
                    <div class="code-tab active" onclick="switchTab('output')">输出</div>
                    <div class="code-tab" onclick="switchTab('code')">代码</div>
                    <div class="code-tab" onclick="switchTab('logs')">日志</div>
                </div>
            </div>

            <!-- 代码内容区域 -->
            <div class="code-content">
                <!-- 执行状态 -->
                <div class="execution-status" id="executionStatus" style="display: none;">
                    正在执行...
                </div>

                <!-- 输出标签页 -->
                <div id="tab-output" class="tab-content">
                    <div class="code-block">
                        <pre id="output">等待任务执行...</pre>
                    </div>
                </div>

                <!-- 代码标签页 -->
                <div id="tab-code" class="tab-content" style="display: none;">
                    <div class="code-block">
                        <pre id="generatedCode">暂无生成的代码</pre>
                    </div>
                </div>

                <!-- 日志标签页 -->
                <div id="tab-logs" class="tab-content" style="display: none;">
                    <div class="code-block">
                        <pre id="logs">系统启动完成
等待用户输入...</pre>
                    </div>
                </div>
            </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentSessionId = `session_${Date.now()}`;
        let websocket = null;

        // 测试用例
        const testCases = {
            data_analysis: "帮我分析销售数据，创建包含日期、产品、销售额的数据集，然后生成月度趋势图表",
            api_call: "调用GitHub API获取microsoft/vscode、facebook/react、tensorflow/tensorflow这些热门仓库的信息",
            web_automation: "使用浏览器自动化访问example.com网站，获取页面标题和链接信息",
            calculation: "计算10万元本金，年利率5%，投资10年的复合利息，比较不同复利频率的收益差异"
        };

        // 当前活动标签页
        let activeTab = 'output';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            connectWebSocket();
            setupInputHandlers();
        });

        // 设置输入处理器
        function setupInputHandlers() {
            const textarea = document.getElementById('userInput');

            // 自动调整高度
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // 回车发送消息
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 检查后端连接
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (data.status === 'healthy') {
                    updateConnectionStatus('connected', '已连接');
                } else {
                    updateConnectionStatus('disconnected', '连接异常');
                }
            } catch (error) {
                updateConnectionStatus('disconnected', '连接失败');
            }
        }

        // 更新连接状态
        function updateConnectionStatus(type, message) {
            const statusEl = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            statusEl.className = `status-badge ${type}`;
            statusText.textContent = message;
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签页状态
            document.querySelectorAll('.code-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

            // 显示对应内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(`tab-${tabName}`).style.display = 'block';

            activeTab = tabName;
        }

        // 发送消息
        async function sendMessage() {
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) return;

            // 添加用户消息到对话
            addMessage('user', userInput);

            // 清空输入框
            document.getElementById('userInput').value = '';
            document.getElementById('userInput').style.height = 'auto';

            // 禁用发送按钮
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;

            // 显示执行状态
            showExecutionStatus('running', '正在处理...');

            try {
                const response = await fetch(`${API_BASE}/api/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        session_id: currentSessionId
                    })
                });

                const result = await response.json();

                // 添加AI回复到对话
                addMessage('assistant', formatResult(result));

                // 更新代码和输出面板
                updateCodePanels(result);

            } catch (error) {
                addMessage('assistant', `执行出错: ${error.message}`);
                showExecutionStatus('error', '执行失败');
            } finally {
                sendButton.disabled = false;
                hideExecutionStatus();
            }
        }

        // 添加消息到对话
        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = type === 'user' ? 'U' : 'AI';
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${content}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 格式化结果
        function formatResult(result) {
            if (result.success && result.result) {
                const res = result.result;
                const intent = res.intent || {};

                let response = `任务已完成！\n\n`;
                response += `📋 任务类型: ${intent.task_type || '未知'}\n`;
                response += `🔧 执行环境: ${intent.execution_env || '未知'}\n`;
                response += `📊 置信度: ${Math.round((intent.confidence || 0) * 100)}%\n`;

                if (res.result?.reasoning) {
                    response += `\n💡 分析: ${res.result.reasoning}`;
                }

                return response;
            } else {
                return `执行失败: ${result.error || '未知错误'}`;
            }
        }

        // 更新代码面板
        function updateCodePanels(result) {
            if (result.success && result.result) {
                const res = result.result;

                // 更新输出
                if (res.result?.output) {
                    document.getElementById('output').textContent = res.result.output;
                }

                // 更新生成的代码
                if (res.result?.code) {
                    document.getElementById('generatedCode').textContent = res.result.code;
                }

                // 添加日志
                const logEntry = `[${new Date().toLocaleTimeString()}] 任务执行完成\n`;
                const logsEl = document.getElementById('logs');
                logsEl.textContent += logEntry;
            }
        }

        // 显示执行状态
        function showExecutionStatus(type, message) {
            const statusEl = document.getElementById('executionStatus');
            statusEl.className = `execution-status ${type}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
        }

        // 隐藏执行状态
        function hideExecutionStatus() {
            document.getElementById('executionStatus').style.display = 'none';
        }

        // 连接WebSocket
        function connectWebSocket() {
            try {
                websocket = new WebSocket(`ws://localhost:8000/ws/${currentSessionId}`);

                websocket.onopen = function() {
                    addLog('WebSocket连接已建立');
                };

                websocket.onmessage = function(event) {
                    const eventData = JSON.parse(event.data);
                    addLog(`收到事件: ${eventData.type || 'Unknown'}`);
                };

                websocket.onclose = function() {
                    addLog('WebSocket连接已断开');
                };

                websocket.onerror = function(error) {
                    addLog(`WebSocket错误: ${error.message || '连接失败'}`);
                };
            } catch (error) {
                console.error('WebSocket连接失败:', error);
                addLog('WebSocket连接失败');
            }
        }

        // 添加日志
        function addLog(message) {
            const logsEl = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logsEl.textContent += `[${timestamp}] ${message}\n`;

            // 自动滚动到底部
            logsEl.scrollTop = logsEl.scrollHeight;
        }

        // 加载测试用例
        function loadTestCase(caseType) {
            const textarea = document.getElementById('userInput');
            textarea.value = testCases[caseType];

            // 自动调整高度
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';

            // 添加视觉反馈
            textarea.style.borderColor = '#3b82f6';
            setTimeout(() => {
                textarea.style.borderColor = '#d1d5db';
            }, 1000);
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <div class="message-avatar">AI</div>
                    <div class="message-content">
                        对话已清空。我是NextAI智能助手，请告诉我你需要什么帮助？
                    </div>
                </div>
            `;

            // 清空输出面板
            document.getElementById('output').textContent = '等待任务执行...';
            document.getElementById('generatedCode').textContent = '暂无生成的代码';
            document.getElementById('logs').textContent = '系统启动完成\n等待用户输入...';
        }
    </script>
</body>
</html>
